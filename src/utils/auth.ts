/**
 * Authentication utilities for API requests
 * Provides consistent authentication headers across all API services
 */

/**
 * Get authentication headers for API requests
 * Returns headers required for authenticated API calls
 */
export const getAuthHeaders = (): Record<string, string> => {
  const token = getToken()

  const headers: Record<string, string> = {}

  // Add authentication token
  if (token) {
    headers['X-Access'] = token
  }

  // Note: Partner filtering is handled via partner_ids parameter in request payload
  // Client ID headers removed as we're using partner-based approach

  return headers
}

/**
 * Get selected partner ID for API filtering
 * Returns the partner ID to be used in partner_ids parameter
 */
export const getSelectedPartnerIds = (): string => {
  const selectedPartnerId = localStorage.getItem('selectedPartnerId')

  // If no partner selected or "all" is selected, return empty string
  if (!selectedPartnerId || selectedPartnerId === 'all') {
    return ''
  }

  return selectedPartnerId
}

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token')
  return !!token
}

/**
 * Get current user token
 */
export const getToken = (): string | null => {
  return localStorage.getItem('token')
}

/**
 * Get selected partner ID
 */
export const getSelectedPartnerId = (): string | null => {
  return localStorage.getItem('selectedPartnerId')
}

/**
 * Set authentication token
 */
export const setToken = (token: string): void => {
  if (token) {
    localStorage.setItem('token', token)
  } else {
    localStorage.removeItem('token')
  }
}

/**
 * Set selected partner ID
 */
export const setSelectedPartnerId = (partnerId: string): void => {
  if (partnerId) {
    localStorage.setItem('selectedPartnerId', partnerId)
  } else {
    localStorage.removeItem('selectedPartnerId')
  }
}

/**
 * Clear selected partner
 */
export const clearSelectedPartner = (): void => {
  localStorage.removeItem('selectedPartnerId')
}

/**
 * Clear authentication data
 */
export const clearAuth = (): void => {
  localStorage.removeItem('token')
  localStorage.removeItem('selectedPartnerId')
  localStorage.removeItem('user')
  localStorage.removeItem('permissions')
  localStorage.removeItem('role')
  localStorage.removeItem('partnerList')
}

/**
 * Get complete authentication context
 */
export const getAuthContext = () => {
  return {
    token: getToken(),
    partnerId: getSelectedPartnerId(),
    isAuthenticated: isAuthenticated()
  }
}

/**
 * Validate authentication headers
 * Checks if required authentication information is present
 */
export const validateAuthHeaders = (): { valid: boolean; missing: string[] } => {
  const missing: string[] = []
  
  if (!getToken()) {
    missing.push('Authentication token')
  }
  
  // Note: Client ID and Partner ID might not always be required
  // depending on the API endpoint and user type
  
  return {
    valid: missing.length === 0,
    missing
  }
}

/**
 * Create authenticated request headers
 * Combines authentication headers with additional headers
 */
export const createAuthHeaders = (additionalHeaders: Record<string, string> = {}): Record<string, string> => {
  return {
    ...getAuthHeaders(),
    ...additionalHeaders
  }
}

/**
 * Authentication error types
 */
export enum AuthErrorType {
  NO_TOKEN = 'NO_TOKEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  EXPIRED_TOKEN = 'EXPIRED_TOKEN',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS'
}

/**
 * Authentication error class
 */
export class AuthError extends Error {
  public type: AuthErrorType
  public statusCode: number

  constructor(type: AuthErrorType, message: string, statusCode: number = 401) {
    super(message)
    this.type = type
    this.statusCode = statusCode
    this.name = 'AuthError'
  }
}

/**
 * Handle authentication errors from API responses
 */
export const handleAuthError = (error: any): AuthError => {
  if (error.response?.status === 401) {
    return new AuthError(
      AuthErrorType.INVALID_TOKEN,
      'Authentication failed. Please login again.',
      401
    )
  }
  
  if (error.response?.status === 403) {
    return new AuthError(
      AuthErrorType.INSUFFICIENT_PERMISSIONS,
      'Insufficient permissions for this action.',
      403
    )
  }
  
  if (error.response?.status === 422) {
    return new AuthError(
      AuthErrorType.NO_TOKEN,
      'Authentication information is incomplete. Please check your request and try again.',
      422
    )
  }
  
  return new AuthError(
    AuthErrorType.INVALID_TOKEN,
    error.message || 'Authentication error occurred.',
    error.response?.status || 401
  )
}

/**
 * Refresh authentication token
 * This would typically call a refresh token endpoint
 */
export const refreshToken = async (): Promise<boolean> => {
  // Implementation would depend on your refresh token strategy
  // For now, return false to indicate refresh failed
  return false
}

/**
 * Auto-logout on authentication failure
 */
export const autoLogout = (): void => {
  clearAuth()
  // Redirect to login page
  window.location.href = '/login'
}

/**
 * Authentication interceptor for API requests
 * Can be used with axios interceptors
 */
export const authInterceptor = {
  request: (config: any) => {
    const authHeaders = getAuthHeaders()
    config.headers = {
      ...config.headers,
      ...authHeaders
    }
    return config
  },
  
  response: (response: any) => {
    return response
  },
  
  error: (error: any) => {
    const authError = handleAuthError(error)
    
    // Auto-logout on certain auth errors
    if (authError.type === AuthErrorType.INVALID_TOKEN || 
        authError.type === AuthErrorType.EXPIRED_TOKEN) {
      autoLogout()
    }
    
    return Promise.reject(authError)
  }
}
